{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "docker:build:dev": "docker build -t sstw-api:dev .", "docker:build:prod": "docker build -t sstw-api:prod .", "docker:dev": "docker compose -f docker-compose.dev.yml up -d", "docker:dev:build": "docker compose -f docker-compose.dev.yml build", "docker:dev:down": "docker compose -f docker-compose.dev.yml down", "docker:dev:logs": "docker compose -f docker-compose.dev.yml logs -f", "docker:prod": "docker compose -f docker-compose.prod.yml up -d", "docker:prod:build": "docker compose -f docker-compose.prod.yml build", "docker:prod:down": "docker compose -f docker-compose.prod.yml down", "docker:prod:logs": "docker compose -f docker-compose.prod.yml logs -f", "docker:stack:dev": "docker stack deploy -c docker-compose.dev.yml sstw-api-dev", "docker:stack:dev:remove": "docker stack rm sstw-api-dev", "docker:stack:dev:services": "docker stack services sstw-api-dev", "docker:stack:dev:ps": "docker stack ps sstw-api-dev", "docker:stack:prod": "docker stack deploy -c docker-compose.prod.yml sstw-api-prod", "docker:stack:prod:remove": "docker stack rm sstw-api-prod", "docker:stack:prod:services": "docker stack services sstw-api-prod", "docker:stack:prod:ps": "docker stack ps sstw-api-prod"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/lib-storage": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.750.0", "@knaadh/nestjs-drizzle-pg": "^1.2.0", "@liaoliaots/nestjs-redis": "^10.0.0", "@nest-lab/throttler-storage-redis": "^1.1.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.10", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.10", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.10", "@nestjs/platform-socket.io": "^11.0.10", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.4", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.0.10", "@paralleldrive/cuid2": "^2.2.2", "@scalar/nestjs-api-reference": "^0.3.186", "@types/cookie-parser": "^1.4.8", "argon2": "^0.41.1", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "dotenv-expand": "^12.0.1", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "eslint-plugin-drizzle": "^0.2.3", "express": "^4.21.2", "firebase-admin": "^13.1.0", "ioredis": "^5.5.0", "nestjs-zod": "^4.3.1", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.13.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.20.0", "@nestjs/cli": "^11.0.4", "@nestjs/schematics": "^11.0.1", "@nestjs/testing": "^11.0.10", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.10", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.13.4", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "drizzle-kit": "^0.30.4", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "^3.8.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "globals": "^15.15.0", "jest": "^29.7.0", "prettier": "^3.5.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}