import '@jest/globals';
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../../src/app.module';
import * as helpers from '../helpers/auth-helpers';

describe('Auth & Registration Workflows', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should register and login as partner', async () => {
    const partner = await helpers.registerPartner(app);
    const partnerLogin = await helpers.loginAsPartner(app, partner);
    expect(partnerLogin).toHaveProperty('accessToken');
    expect(partnerLogin).toHaveProperty('refreshToken');
  });

  it('should register worker via invite code workflow and login', async () => {
    // Register partner and login
    const partner = await helpers.registerPartner(app);
    const partnerLogin = await helpers.loginAsPartner(app, partner);
    // Generate invite code for worker
    const inviteCode = await helpers.generateInviteCode(
      app,
      partnerLogin.accessToken,
      'worker',
    );
    // Register worker
    const worker = await helpers.registerWorker(app, inviteCode);
    // Approve registration request
    await helpers.approveRegistrationRequest(
      app,
      partnerLogin.accessToken,
      worker.id,
    );
    // Login as worker
    const workerLogin = await helpers.loginAsWorker(app, worker);
    expect(workerLogin).toHaveProperty('accessToken');
    expect(workerLogin).toHaveProperty('refreshToken');
  });

  it('should register manager via invite code workflow and login', async () => {
    // Register partner and login
    const partner = await helpers.registerPartner(app);
    const partnerLogin = await helpers.loginAsPartner(app, partner);
    // Generate invite code for manager
    const inviteCode = await helpers.generateInviteCode(
      app,
      partnerLogin.accessToken,
      'manager',
    );
    // Register manager
    const manager = await helpers.registerManager(app, inviteCode);
    // Approve registration request
    await helpers.approveRegistrationRequest(
      app,
      partnerLogin.accessToken,
      manager.id,
    );
    // Login as manager
    const managerLogin = await helpers.loginAsManager(app, manager);
    expect(managerLogin).toHaveProperty('accessToken');
    expect(managerLogin).toHaveProperty('refreshToken');
  });
});
