# Real-Time Updates (WebSocket API)

This document describes the WebSocket API for receiving real-time updates from the server.

## Connection Details

**Namespace:** `/updates`
**Supported Transports:** WebSocket, Polling
**CORS:** Enabled (all origins)

### Heartbeat Configuration

The server uses a heartbeat mechanism to maintain connection health:

- `pingInterval`: 25000ms (25 seconds) - How often the server sends ping packets
- `pingTimeout`: 10000ms (10 seconds) - How long to wait for a pong response

If a client fails to respond to a ping within the timeout period, the connection will be automatically terminated.

## Authentication

Authentication is required to establish a WebSocket connection. Provide a JWT token in the connection handshake:

```javascript
const socket = io('/updates', {
  auth: {
    token: 'your-jwt-token',
  },
  // Optional: Override default heartbeat settings
  pingTimeout: 10000,
  pingInterval: 25000,
});
```

If authentication fails, the connection will be rejected.

## Room Structure

### Room Types

- `user:{userId}` - Personal user room
- `worker:{workerId}` - Worker-specific room
- `partner:{partnerId}` - Partner-specific room
- `project:{projectId}` - Project-specific room
- `manager:{managerId}` - Manager-specific room

### Automatic Room Assignment

Users are automatically joined to relevant rooms based on their role:

#### Workers

- `user:{userId}`
- `worker:{workerId}`
- `project:{projectId}` (if assigned to a project and employment status is active/notice period)

#### Partners

- `user:{userId}`
- `partner:{partnerId}`
- `project:{projectId}` (for all partner's projects)

#### Managers

- `user:{userId}`
- `manager:{managerId}`
- `project:{projectId}` (for projects managed by this manager)

## Events

### User Updates

#### `user.updated`

**Target Rooms:**

- `user:{userId}` (user's personal room)
- `{roleType}:{entityId}` (role-specific room)
- If user is a worker, also targets:
  - `worker:{workerId}`
  - `project:{projectId}` (if assigned)
  - `partner:{partnerId}` (if associated)

```typescript
{
  type: 'user.updated',
  payload: {
    userId: string,
    action?: 'email_changed' | 'phone_changed' | 'personal_info_changed' | 'verification_changed' | string,
    changes?: {
      email?: boolean,
      phoneNumber?: boolean,
      isEmailVerified?: boolean,
      isPhoneVerified?: boolean,
      personalInfo?: boolean
    }
  }
}
```

### Worker Updates

#### `worker.updated`

**Target Rooms:**

- `worker:{workerId}`
- `user:{userId}`
- `project:{projectId}` (current and/or new project)
- `partner:{partnerId}` (if associated)

```typescript
{
  type: 'worker.updated',
  payload: {
    workerId: string,
    projectId?: string | null,
    action?: 'project_assigned' | 'project_unassigned' | 'user_info_changed' | string,
    changes?: {
      email?: boolean,
      phoneNumber?: boolean,
      isEmailVerified?: boolean,
      isPhoneVerified?: boolean,
      personalInfo?: boolean
    }
  }
}
```

**Actions:**
- `project_assigned`: Worker was assigned to a project
- `project_unassigned`: Worker was removed from a project
- `user_info_changed`: Worker's user information was updated (includes changes to email, phone, verification status, or personal info)

#### `worker.employment_changed`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'worker.employment_changed',
  payload: {
    workerId: string,
    status: 'active' | 'inactive' | 'quit' | 'terminated' | 'quit_notice' | 'terminated_notice',
    effectiveEndDate?: Date
  }
}
```

#### `worker.approval_changed`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'worker.approval_changed',
  payload: {
    workerId: string,
    status: 'approved' | 'rejected' | 'pending'
  }
}
```

#### `worker.status_changed`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'worker.status_changed',
  payload: {
    workerId: string,
    status: 'started' | 'finished' | 'paused' | 'passive'
  }
}
```

#### `worker.presence_changed`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'worker.presence_changed',
  payload: {
    workerId: string,
    status: 'validated' | 'empty' | 'late'
  }
}
```

#### `presence_validation.requested`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'presence_validation.requested',
  payload: {
    validationId: string
  }
}
```

### Daily Report Updates

#### `daily_report.created`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'daily_report.created',
  payload: {
    reportId: string
  }
}
```

#### `daily_report.updated`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'daily_report.updated',
  payload: {
    reportId: string,
    status: 'approved' | 'declined' | 'submitted' | 'pending'
  }
}
```

#### `daily_report.finished`

**Target Rooms:**

- `worker:{workerId}`
- Associated project and partner rooms

```typescript
{
  type: 'daily_report.finished',
  payload: {
    reportId: string,
    workerId: string
  }
}
```

This event is emitted when a daily report is finished, either by the worker themselves or by their partner. Clients should use this event to refresh their timer/report status.

### Project Updates

#### `project.created`

**Target Rooms:**

- `project:{projectId}`
- `partner:{partnerId}`
- All sockets in the partner room are joined to the project room

```typescript
{
  type: 'project.created',
  payload: {
    projectId: string,
    partnerId: string
  }
}
```

#### `project.updated`

**Target Rooms:**

- `project:{projectId}`
- `partner:{partnerId}`

```typescript
{
  type: 'project.updated',
  payload: {
    projectId: string,
    partnerId: string
  }
}
```

#### `project.deleted`

**Target Rooms:**

- `project:{projectId}` (before room deletion)
- `partner:{partnerId}`
- All sockets are removed from the project room after notification

```typescript
{
  type: 'project.deleted',
  payload: {
    projectId: string,
    partnerId: string
  }
}
```

### Partner Updates

#### `partner.updated`

**Target Rooms:**

- `partner:{partnerId}`

```typescript
{
  type: 'partner.updated',
  payload: {
    partnerId: string
  }
}
```

### Manager Updates

#### `manager.updated`

**Target Rooms:**
- `manager:{managerId}`

```typescript
{
  type: 'manager.updated',
  payload: {
    managerId: string,
    changes?: {
      approvalState?: boolean,
      permissionType?: boolean,
      userInfo?: boolean
    }
  }
}
```

#### `manager.assigned_to_project`

**Target Rooms:**
- `manager:{managerId}`
- `project:{projectId}`

```typescript
{
  type: 'manager.assigned_to_project',
  payload: {
    managerId: string,
    projectId: string
  }
}
```

#### `manager.removed_from_project`

**Target Rooms:**
- `manager:{managerId}`
- `project:{projectId}`

```typescript
{
  type: 'manager.removed_from_project',
  payload: {
    managerId: string,
    projectId: string
  }
}
```

#### `manager.employment_changed`

**Target Rooms:**
- `manager:{managerId}`
- Associated project rooms

```typescript
{
  type: 'manager.employment_changed',
  payload: {
    managerId: string,
    status: 'active' | 'inactive'
  }
}
```

### User Deletion

#### `user.deleted`

**Target Rooms:**
- Relevant role-specific rooms

```typescript
{
  type: 'user.deleted',
  payload: {
    userId: string
  }
}
```

### Registration Requests

#### `registration_request.received`

**Target Rooms:**
- Partner and manager rooms associated with the registration request

```typescript
{
  type: 'registration_request.received',
  payload: {
    requestId: string
  }
}
```

## Error Handling

The server may emit error events in the following cases:

- Authentication failure
- Invalid token
- Connection timeout
- Server errors

It's recommended to implement proper error handling and reconnection logic in your client application.

## Example Usage

```javascript
const socket = io('/updates', {
  auth: {
    token: 'your-jwt-token',
  },
});

// Handle connection
socket.on('connect', () => {
  console.log('Connected to updates service');
});

// Listen for worker updates
socket.on('worker.updated', (payload) => {
  console.log('Worker updated:', payload);
});

// Handle errors
socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});
```

## Best Practices

1. **Room Management**

   - Be aware of room membership lifecycle
   - Handle room joins/leaves appropriately
   - Monitor room event propagation

2. **Connection Management**

   - Implement reconnection logic
   - Handle connection errors gracefully
   - Clean up listeners when disconnecting

3. **Event Handling**

   - Validate payload data before processing
   - Implement error handling for each event type
   - Use TypeScript interfaces for type safety

4. **Security**
   - Always use HTTPS/WSS in production
   - Keep JWT tokens secure
   - Validate all incoming data

> **Note:** Only one manager per project is supported. Assigning a new manager to a project will remove the previous one.
