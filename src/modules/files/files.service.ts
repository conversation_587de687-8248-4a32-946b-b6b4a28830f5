import * as path from 'path';
import { Readable } from 'stream';

import { DeleteObjectCommand, GetObjectCommand, S3 } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { add } from 'date-fns';
import { eq } from 'drizzle-orm';
import { Response } from 'express';

import { Forwarded } from '@/common/types';
import { generateId } from '@/common/utils/generateId';

import { Database } from '../db/db.module';
import { files } from '../db/entities/file.entity';
import { FilePermissionsService } from '../file-permissions/file-permissions.service';
import { ManagersService } from '../managers/managers.service';
import { PartnersService } from '../partners/partners.service';
import { WorkersService } from '../workers/workers.service';
import { CreateFileDto } from './dto/create-file.dto';
import { UploadFileParamsDto } from './dto/upload-file-params.dto';

@Injectable()
export class FilesService {
  private readonly s3: S3;
  private readonly AWS_S3_BUCKET: string;

  constructor(
    @Inject('DB') private readonly db: Database,
    private readonly configService: ConfigService,
    private readonly filePermissionsService: FilePermissionsService,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
    @Inject(forwardRef(() => PartnersService))
    private readonly partnersService: Forwarded<PartnersService>,
  ) {
    if (!this.configService.get<string>('s3.bucket'))
      throw new Error('S3 bucket not configured');
    if (!this.configService.get<string>('s3.accessKeyId'))
      throw new Error('S3 access key ID not configured');
    if (!this.configService.get<string>('s3.secretAccessKey'))
      throw new Error('S3 secret access key not configured');

    this.AWS_S3_BUCKET = this.configService.get<string>('s3.bucket')!;
    this.s3 = new S3({
      credentials: {
        accessKeyId: this.configService.get<string>('s3.accessKeyId')!,
        secretAccessKey: this.configService.get<string>('s3.secretAccessKey')!,
      },
      region: 'eu-north-1',
    });
  }

  async create(createFileDto: CreateFileDto) {
    return (
      await this.db
        .insert(files)
        .values(createFileDto)
        .returning({ id: files.id })
    )[0];
  }

  findOne(id: string) {
    return this.db.query.files.findFirst({
      where: (files, { eq }) => eq(files.id, id),
      with: {
        permissions: true,
      },
    });
  }

  delete(id: string) {
    return this.db
      .update(files)
      .set({ deletedAt: new Date() })
      .where(eq(files.id, id));
  }

  async uploadFile(
    // @ts-expect-error
    file: Express.Multer.File,
    params: UploadFileParamsDto,
    userId: string,
  ) {
    const safeKey = this.generateSafeKey(file.originalname);
    const security = params.security ?? 'private';
    try {
      const s3Response = await new Upload({
        client: this.s3,
        params: {
          Bucket: this.AWS_S3_BUCKET,
          Key: safeKey,
          Body: file.buffer,
          ContentType: file.mimetype,
          ContentDisposition: 'inline',
          ACL: security === 'public' ? 'public-read' : 'private',
        },
      }).done();
      const fileId = await this.create({
        s3Key: safeKey,
        fileName: file.originalname,
        fileType: file.mimetype,
        publicFileUrl: security === 'public' ? s3Response.Location : null,
        scope: params.scope,
        security,
      });

      switch (params.scope) {
        case 'user':
          await this.filePermissionsService.create({
            fileId: fileId.id,
            userId: userId,
            scope: 'user',
          });
          break;
        case 'partner':
          const worker = await this.workersService.findOneByUserId(userId);
          const partner = await this.partnersService.findOneByUserId(userId);
          const manager = await this.managersService.findOneByUserId(userId);

          let partnerUserId: string | undefined;

          if (worker?.partner?.user?.id) {
            partnerUserId = worker.partner.user.id;
          } else if (partner) {
            partnerUserId = userId;
          } else if (manager?.partner?.userId ?? manager?.partnerId) {
            const managerPartner =
              manager.partner ??
              (await this.partnersService.findOne(manager.partnerId));
            if (!managerPartner) {
              throw new NotFoundException('Partner not found for manager');
            }
            partnerUserId = managerPartner.userId;
          } else {
            throw new NotFoundException(
              'No valid partner relationship found for user',
            );
          }

          await this.filePermissionsService.create(
            {
              fileId: fileId.id,
              userId: partnerUserId,
              scope: 'partner',
            },
            {
              fileId: fileId.id,
              userId,
              scope: 'user',
            },
          );
          break;
        default:
          break;
      }
      return fileId;
    } catch (e) {
      console.error('Upload error:', e);
      throw new InternalServerErrorException('Failed to upload file');
    }
  }

  async checkFileExists(id: string) {
    const file = await this.findOne(id);
    if (!file) return null;
    try {
      await this.s3.send(
        new GetObjectCommand({
          Bucket: this.AWS_S3_BUCKET,
          Key: file.s3Key,
        }),
      );
      return file;
    } catch (e) {
      console.error('Check error:', e);
      return null;
    }
  }

  async deleteFile(id: string) {
    const file = await this.checkFileExists(id);
    if (!file) throw new NotFoundException('File not found');

    try {
      await this.s3.send(
        new DeleteObjectCommand({
          Bucket: this.AWS_S3_BUCKET,
          Key: file.s3Key,
        }),
      );
    } catch (e) {
      console.error('Delete error:', e);
      throw new Error('Failed to delete file');
    }
  }

  async streamFile(id: string, res: Response) {
    const file = await this.checkFileExists(id);
    if (!file) throw new NotFoundException('File not found');

    try {
      const command = new GetObjectCommand({
        Bucket: this.AWS_S3_BUCKET,
        Key: file.s3Key,
      });

      const s3Object = await this.s3.send(command);

      res.setHeader(
        'Content-Type',
        s3Object.ContentType || 'application/octet-stream',
      );
      res.setHeader('Content-Disposition', 'inline');

      const body = s3Object.Body;
      if (body instanceof Readable) {
        body.pipe(res);
      } else if (body instanceof Blob) {
        const buffer = await body.arrayBuffer();
        const readable = Readable.from(Buffer.from(buffer));
        readable.pipe(res);
      } else {
        throw new Error('Unexpected response body type');
      }
    } catch (e) {
      console.error(e);
      res.status(404).send('File not found');
    }
  }

  async generatePresignedUrl(id: string) {
    const file = await this.checkFileExists(id);
    if (!file) throw new NotFoundException('File not found');
    try {
      const command = new GetObjectCommand({
        Bucket: this.AWS_S3_BUCKET,
        Key: file.s3Key,
      });

      const signedUrl = await getSignedUrl(this.s3, command, {
        expiresIn: 60 * 60 * 1,
      });
      return {
        url: signedUrl,
        expiresAt: add(new Date(), {
          seconds: 60 * 60 * 1,
        }),
      };
    } catch (e) {
      console.error(e);
      throw new Error('Failed to generate pre-signed URL');
    }
  }

  async getFileUrl(fileId: string, userId: string) {
    const file = await this.checkFileExists(fileId);
    if (!file) throw new NotFoundException('File not found');

    if (file.security === 'public' && file.publicFileUrl) {
      return {
        url: file.publicFileUrl,
        expiresAt: null,
      };
    }

    const hasDirectPermission = file.permissions
      .filter((p) => p.scope === file.scope || p.scope === 'user')
      .find((p) => p.userId === userId);

    if (hasDirectPermission) {
      return this.generatePresignedUrl(fileId);
    }

    if (file.scope === 'partner') {
      const partnerPermission = file.permissions.find(
        (p) => p.scope === 'partner',
      );
      if (!partnerPermission) {
        throw new ForbiddenException(
          'No partner permission found for this file',
        );
      }
      const partnerUserId = partnerPermission.userId;

      const worker = await this.workersService.findOneByUserId(userId);
      const manager = await this.managersService.findOneByUserId(userId);
      const partner = await this.partnersService.findOneByUserId(userId);

      const hasAccess =
        (worker && worker.partner.user.id === partnerUserId) ||
        (manager && manager.partner.userId === partnerUserId) ||
        (partner && partner.userId === partnerUserId);

      if (hasAccess) {
        return this.generatePresignedUrl(fileId);
      }
    }

    throw new ForbiddenException(
      'You do not have permission to access this file',
    );
  }

  private generateSafeKey(originalName: string): string {
    const cleanFileName = originalName
      .replace(/[^a-zA-Z0-9_\-.]+/g, '')
      .replace(/\/+/g, '-')
      .replace(/^\.+/, '');

    const extension = path.extname(cleanFileName);
    const baseName = path.basename(cleanFileName, extension);
    const uniqueId = generateId();

    return `${uniqueId}-${baseName}${extension}`.substring(0, 255);
  }
}
