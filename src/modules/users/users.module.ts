import { Module, forwardRef } from '@nestjs/common';

import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { CredentialVerificationModule } from '../credential-verification/credential-verification.module';
import { SecurityNotificationsModule } from '../security-notifications/security-notifications.module';
import { UpdatesModule } from '../updates/updates.module';
import { ManagersModule } from '../managers/managers.module';

@Module({
  imports: [
    forwardRef(() => CredentialVerificationModule),
    forwardRef(() => UpdatesModule),
    forwardRef(() => SecurityNotificationsModule),
    forwardRef(() => ManagersModule),
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
