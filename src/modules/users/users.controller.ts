import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { AllowUnverifiedEmail } from '@/common/decorators/allow-unverified-email.decorator';
import { User } from '@/common/decorators/user.decorator';

import {
  UpdatePersonalInfoDto,
  UpdateUserEmailDto,
} from './dto/update-user.dto';
import { UserDto, UserMetaDataDto } from './dto/user.dto';
import { UsersService } from './users.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { RolesGuard } from '@/common/guards/roles.guard';
import { Role } from '@/common/enums';

@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('me')
  @AllowUnverifiedEmail()
  @ApiOperation({
    summary: 'Get current user profile',
    description: 'Retrieves the profile information of the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User profile retrieved successfully',
    type: UserDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
  })
  async getMe(@User() user: RequestUserType): Promise<UserDto> {
    const userData = await this.usersService.findOne(user.id);
    if (!userData) throw new NotFoundException('User not found');
    return userData;
  }

  @Delete('me')
  @ApiOperation({
    summary: 'Delete current user',
    description: 'Delete the authenticated user',
  })
  @UseGuards(RolesGuard)
  @Roles(Role.Manager) //TODO remove this guard in the future, for now its only for managers
  async deleteMe(@User() user: RequestUserType) {
    return this.usersService.deleteUser(user.id);
  }

  @Get('me/metadata')
  @AllowUnverifiedEmail()
  @ApiOperation({
    summary: 'Get current user metadata',
    description:
      'Retrieves the metadata of the authenticated user, including role information and verification status',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User metadata retrieved successfully',
    type: UserMetaDataDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
  })
  getMeMetaData(@User() user: RequestUserType): Promise<UserMetaDataDto> {
    return this.usersService.getUserMetaData(user.id);
  }

  @Patch('me')
  @ApiOperation({
    summary: 'Update current user profile',
    description: 'Update the profile information of the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User profile updated successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
  })
  @ApiNotFoundResponse({
    description: 'User was not found',
  })
  updatePersonalInfo(
    @Body() updateUserDto: UpdatePersonalInfoDto,
    @User() user: RequestUserType,
  ) {
    return this.usersService.updatePersonalInfo(user.id, updateUserDto);
  }

  @Patch('me/email')
  @AllowUnverifiedEmail()
  @ApiOperation({
    summary: 'Update current user email',
    description: 'Update the email address of the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User email updated successfully',
  })
  @ApiBadRequestResponse({
    description: 'Invalid input data',
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
  })
  @ApiNotFoundResponse({
    description: 'User was not found',
  })
  updateEmail(
    @Body() updateEmailDto: UpdateUserEmailDto,
    @User() user: RequestUserType,
  ) {
    return this.usersService.updateEmail(user.id, updateEmailDto);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve a specific user by their ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the user',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User found and returned successfully',
    type: UserDto,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
  })
  @ApiUnauthorizedResponse({
    description: 'Not authenticated',
  })
  async findOne(@Param('id') id: string): Promise<UserDto> {
    const user = await this.usersService.findOne(id);
    if (!user) throw new NotFoundException('User not found');
    return user;
  }
}
