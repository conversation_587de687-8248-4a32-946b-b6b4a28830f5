import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';

import { managers } from '@/modules/db/entities/manager.entity';
import { z } from 'zod';
import { projectManagers } from '@/modules/db/entities/project-manager.entity';
import { users } from '@/modules/db/entities/user.entity';

export const getManagerschema = createSelectSchema(managers);

export const projectManagerSchema = createSelectSchema(projectManagers);
export const managerWithProjectsSchema = getManagerschema.extend({
  projectManagers: z.array(projectManagerSchema),
});

export const userSchema = createSelectSchema(users);

export const managerWithUserSchema = getManagerschema.extend({
  user: userSchema.omit({
    id: true,
    hashedPassword: true,
    avatar: true,
    documentScan: true,
    isEmailVerified: true,
    isPhoneVerified: true,
    role: true,
  }),
});

export const managerWithBasicUserSchema = getManagerschema
  .pick({
    id: true,
    approvalState: true,
    permissionType: true,
    partnerId: true,
  })
  .extend({
    user: createSelectSchema(users).pick({
      id: true,
      firstName: true,
      lastName: true,
      avatarId: true,
      phoneNumber: true,
    }),
  });

export const managerWithUserAndCompanySchema = getManagerschema
  .pick({
    partnerId: true,
    approvalState: true,
    permissionType: true,
    employmentStatus: true,
    endEmploymentDate: true,
  })
  .extend({
    companyName: z.string().nullable(),
    companyAvatarId: z.string().nullable(),
    user: createSelectSchema(users).omit({
      id: true,
      hashedPassword: true,
      avatar: true,
      documentScan: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      role: true,
    }),
  });

export class ManagerDto extends createZodDto(getManagerschema) {}
export class ManagerWithProjectsDto extends createZodDto(
  managerWithProjectsSchema,
) {}
export class ManagerWithUserDto extends createZodDto(managerWithUserSchema) {}
export class ManagerWithBasicUserDto extends createZodDto(
  managerWithBasicUserSchema,
) {}
export class ManagerWithUserAndCompanyDto extends createZodDto(
  managerWithUserAndCompanySchema,
) {}
