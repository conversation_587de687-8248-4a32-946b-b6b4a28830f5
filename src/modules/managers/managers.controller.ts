import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { CanManage } from '@/common/decorators/management.decorator';
import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import {
  ManagementGuard,
  ResourceType,
} from '@/common/guards/management.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';

import { ManagersService } from './managers.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { ProjectsService } from '../projects/projects.service';
import { WorkersService } from '../workers/workers.service';
import { ManagerFilterParamsDto } from './dto/manager-filter-params.dto';
import { ManagerWithBasicUserDto } from './dto/manager.dto';
import { ManagerWithUserAndCompanyDto } from './dto/manager.dto';
import { UpdateManagersWorkerDto } from './dto/update-managers-worker.dto';
import { ManagerWithUserDto } from './dto/manager.dto';
import { FireWorkerDto } from '../partners/dto/fire-worker.dto';

@ApiTags('Managers')
@ApiBearerAuth()
@Controller('managers')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ManagersController {
  constructor(
    private readonly managersService: ManagersService,
    private readonly workersService: WorkersService,
    private readonly projectsService: ProjectsService,
  ) {}

  @Get()
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'List all managers for the authenticated partner',
    description:
      'Retrieve a list of managers with optional filtering capabilities',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Managers retrieved successfully',
    type: [ManagerWithBasicUserDto],
  })
  async findAll(
    @User() user: RequestUserType,
    @Query() filterParams: ManagerFilterParamsDto,
  ): Promise<ManagerWithBasicUserDto[]> {
    let partnerId: string | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      partnerId = await this.managersService.getPartnerId(user.entityId);
    }

    if (!partnerId) {
      throw new NotFoundException('Partner not found');
    }

    return this.managersService.findAllByPartner(partnerId, filterParams);
  }

  @Get('me')
  @Roles(Role.Manager)
  @ApiOperation({
    summary: 'Get current manager profile',
    description:
      'Retrieve detailed information about the authenticated manager',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager profile retrieved successfully',
    type: ManagerWithUserAndCompanyDto,
  })
  async findMe(
    @User() user: RequestUserType,
  ): Promise<ManagerWithUserAndCompanyDto> {
    const manager = await this.managersService.findOneWithUserAndCompany(
      user.entityId,
    );
    if (!manager) throw new NotFoundException('Manager not found');
    return manager;
  }

  @Get(':managerId')
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'Get manager by ID',
    description:
      'Retrieve detailed information about a specific manager for the authenticated partner',
  })
  @ApiParam({
    name: 'managerId',
    description: 'Manager unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager retrieved successfully',
    type: ManagerWithUserDto,
  })
  async findOne(
    @User() user: RequestUserType,
    @Param('managerId') managerId: string,
  ): Promise<ManagerWithUserDto> {
    let partnerId: string | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      partnerId = await this.managersService.getPartnerId(user.entityId);
    }

    const manager = await this.managersService.findOneWithUser(managerId);
    if (!manager) {
      throw new NotFoundException('Manager not found');
    }
    if (manager.partnerId !== partnerId) {
      throw new ForbiddenException('Not authorized');
    }

    return manager;
  }

  @Get('me/projects')
  @Roles(Role.Manager)
  @ApiOperation({
    summary: 'Get managed projects',
    description: 'Retrieve all projects managed by the authenticated manager',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of managed projects retrieved successfully',
  })
  async getManagedProjects(@User() user: RequestUserType) {
    return this.managersService.getManagedProjects(user.entityId);
  }

  @Patch('me/workers/:workerId')
  @Roles({
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Worker)
  @ApiOperation({
    summary: 'Update worker information',
    description: 'Update information for a worker managed by this manager',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker information updated successfully',
  })
  updateWorkerInfo(
    @Param('workerId') workerId: string,
    @Body() updateWorkerDto: UpdateManagersWorkerDto,
  ) {
    return this.workersService.update(workerId, updateWorkerDto);
  }

  @Patch('me/workers/:workerId/fire')
  @Roles({
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @UseGuards(ManagementGuard)
  @CanManage(ResourceType.Worker)
  @ApiOperation({
    summary: 'Fire worker',
    description: 'Fire a worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Worker fired successfully',
  })
  async fireWorker(
    @User() user: RequestUserType,
    @Param('workerId') workerId: string,
    @Body() fireWorkerDto: FireWorkerDto,
  ) {
    return this.workersService.endEmployment(
      workerId,
      'terminated',
      user.id,
      fireWorkerDto.endDate,
      fireWorkerDto.reason,
    );
  }

  @Patch('me/quit')
  @Roles(Role.Manager)
  @ApiOperation({
    summary: 'Quit as manager',
    description: 'Manager voluntarily ends their employment',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employment quit successful',
  })
  async quitEmployment(@User() user: RequestUserType) {
    await this.managersService.quitEmployment(user.entityId, user.id);
    return { message: 'Quit successful' };
  }

  @Patch(':managerId/terminate')
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  // @UseGuards(ManagementGuard)
  // @CanManage(ResourceType.Manager) TODO fix this in the future, for now the logic is in the service
  @ApiOperation({
    summary: 'Terminate manager',
    description: 'Partner terminates a manager',
  })
  @ApiParam({
    name: 'managerId',
    description: 'Manager unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Manager termination successful',
  })
  async terminateEmployment(
    @User() user: RequestUserType,
    @Param('managerId') managerId: string,
  ) {
    await this.managersService.terminateEmployment(managerId, user);
    return { message: 'Termination successful' };
  }
}
