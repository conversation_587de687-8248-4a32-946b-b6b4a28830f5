import { Module, forwardRef } from '@nestjs/common';

import { ManagersController } from './managers.controller';
import { ManagersService } from './managers.service';
import { PartnersModule } from '../partners/partners.module';
import { ProjectsModule } from '../projects/projects.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';
import { UpdatesModule } from '../updates/updates.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { EmploymentHistoryModule } from '../employment-history/employment-history.module';

@Module({
  imports: [
    WorkersModule,
    forwardRef(() => ProjectsModule),
    forwardRef(() => PartnersModule),
    forwardRef(() => UsersModule),
    forwardRef(() => UpdatesModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => EmploymentHistoryModule),
  ],
  controllers: [ManagersController],
  providers: [ManagersService],
  exports: [ManagersService],
})
export class ManagersModule {}
