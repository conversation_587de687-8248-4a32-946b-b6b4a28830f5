export enum UpdatesEvents {
  DAILY_REPORT_CREATED = 'daily_report.created',
  DAILY_REPORT_UPDATED = 'daily_report.updated',
  DAILY_REPORT_FINISHED = 'daily_report.finished',
  PRESENCE_VALIDATION_REQUESTED = 'presence_validation.requested',
  WORKER_STATUS_CHANGED = 'worker.status_changed',
  PROJECT_UPDATED = 'project.updated',
  PROJECT_CREATED = 'project.created',
  PROJECT_DELETED = 'project.deleted',
  REGISTRATION_REQUEST_RECEIVED = 'registration_request.received',
  USER_UPDATED = 'user.updated',
  WORKER_UPDATED = 'worker.updated',
  PARTNER_UPDATED = 'partner.updated',
  WORKER_EMPLOYMENT_CHANGED = 'worker.employment_changed',
  WORKER_APPROVAL_CHANGED = 'worker.approval_changed',
  WORKER_PRESENCE_CHANGED = 'worker.presence_changed',
  MANAGER_UPDATED = 'manager.updated',
  MANAGER_ASSIGNED_TO_PROJECT = 'manager.assigned_to_project',
  MANAGER_REMOVED_FROM_PROJECT = 'manager.removed_from_project',
  MANAGER_EMPLOYMENT_CHANGED = 'manager.employment_changed',
  USER_DELETED = 'user.deleted',
  PROJECT_MANAGER_UPDATED = 'project_manager.updated',
}

interface DailyReportCreatedPayload {
  reportId: string;
}

interface DailyReportUpdatedPayload {
  reportId: string;
  status: 'approved' | 'declined' | 'submitted' | 'pending';
}

interface DailyReportFinishedPayload {
  reportId: string;
  workerId: string;
}

interface PresenceValidationRequestedPayload {
  validationId: string;
}

interface WorkerStatusChangedPayload {
  workerId: string;
  status: 'started' | 'finished' | 'paused' | 'passive';
}

interface ProjectUpdatedPayload {
  projectId: string;
  partnerId: string;
}

interface ProjectCreatedPayload {
  projectId: string;
  partnerId: string;
}

interface ProjectDeletedPayload {
  projectId: string;
  partnerId: string;
}

interface RegistrationRequestReceivedPayload {
  requestId: string;
}

interface UserUpdatedPayload {
  userId: string;
  action?:
    | 'email_changed'
    | 'phone_changed'
    | 'personal_info_changed'
    | 'verification_changed'
    | string;
  changes?: {
    email?: boolean;
    phoneNumber?: boolean;
    isEmailVerified?: boolean;
    isPhoneVerified?: boolean;
    personalInfo?: boolean;
  };
}

export interface WorkerUpdatedPayload {
  workerId: string;
  projectId?: string | null;
  action?:
    | 'project_assigned'
    | 'project_unassigned'
    | 'user_info_changed'
    | string;
  changes?: {
    email?: boolean;
    phoneNumber?: boolean;
    isEmailVerified?: boolean;
    isPhoneVerified?: boolean;
    personalInfo?: boolean;
  };
}

export interface WorkerEmploymentChangedPayload {
  workerId: string;
  status:
    | 'active'
    | 'inactive'
    | 'quit'
    | 'terminated'
    | 'quit_notice'
    | 'terminated_notice';
  effectiveEndDate?: Date;
}

export interface WorkerApprovalChangedPayload {
  workerId: string;
  status: 'approved' | 'rejected' | 'pending';
}

export interface WorkerPresenceChangedPayload {
  workerId: string;
  status: 'validated' | 'empty' | 'late';
}

interface PartnerUpdatedPayload {
  partnerId: string;
}

export interface ManagerUpdatedPayload {
  managerId: string;
  changes?: {
    approvalState?: boolean;
    permissionType?: boolean;
    userInfo?: boolean;
  };
}

export interface ManagerAssignedToProjectPayload {
  managerId: string;
  projectId: string;
}

export interface ManagerRemovedFromProjectPayload {
  managerId: string;
  projectId: string;
}

export interface ManagerEmploymentChangedPayload {
  managerId: string;
  status:
    | 'active'
    | 'inactive'
    | 'quit'
    | 'terminated'
    | 'quit_notice'
    | 'terminated_notice';
  effectiveEndDate?: Date;
}

export interface ProjectManagerUpdatedPayload {
  projectId: string;
  managerId: string;
  action: 'assigned' | 'removed';
}

interface UserDeletedPayload {
  userId: string;
}

export type UpdatesEventPayloads = {
  [UpdatesEvents.DAILY_REPORT_CREATED]: DailyReportCreatedPayload;
  [UpdatesEvents.DAILY_REPORT_UPDATED]: DailyReportUpdatedPayload;
  [UpdatesEvents.DAILY_REPORT_FINISHED]: DailyReportFinishedPayload;
  [UpdatesEvents.PRESENCE_VALIDATION_REQUESTED]: PresenceValidationRequestedPayload;
  [UpdatesEvents.WORKER_STATUS_CHANGED]: WorkerStatusChangedPayload;
  [UpdatesEvents.PROJECT_UPDATED]: ProjectUpdatedPayload;
  [UpdatesEvents.PROJECT_CREATED]: ProjectCreatedPayload;
  [UpdatesEvents.PROJECT_DELETED]: ProjectDeletedPayload;
  [UpdatesEvents.REGISTRATION_REQUEST_RECEIVED]: RegistrationRequestReceivedPayload;
  [UpdatesEvents.USER_UPDATED]: UserUpdatedPayload;
  [UpdatesEvents.WORKER_UPDATED]: WorkerUpdatedPayload;
  [UpdatesEvents.PARTNER_UPDATED]: PartnerUpdatedPayload;
  [UpdatesEvents.WORKER_EMPLOYMENT_CHANGED]: WorkerEmploymentChangedPayload;
  [UpdatesEvents.WORKER_APPROVAL_CHANGED]: WorkerApprovalChangedPayload;
  [UpdatesEvents.WORKER_PRESENCE_CHANGED]: WorkerPresenceChangedPayload;
  [UpdatesEvents.MANAGER_UPDATED]: ManagerUpdatedPayload;
  [UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT]: ManagerAssignedToProjectPayload;
  [UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT]: ManagerRemovedFromProjectPayload;
  [UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED]: ManagerEmploymentChangedPayload;
  [UpdatesEvents.USER_DELETED]: UserDeletedPayload;
  [UpdatesEvents.PROJECT_MANAGER_UPDATED]: ProjectManagerUpdatedPayload;
};

export type UpdatesMessageBody<T extends UpdatesEvents> = {
  type: T;
  payload: UpdatesEventPayloads[T];
};
