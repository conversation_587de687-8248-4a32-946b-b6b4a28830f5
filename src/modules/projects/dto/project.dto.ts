import { createSelectSchema } from 'drizzle-zod';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { projects } from '@/modules/db/entities/project.entity';
import { getWorkerWithUserSchema } from '@/modules/workers/dto/worker.dto';
import { managerWithBasicUserSchema } from '@/modules/managers/dto/manager.dto';

export const getProjectSchema = createSelectSchema(projects).pick({
  id: true,
  name: true,
  description: true,
  address: true,
  createdAt: true,
  partnerId: true,
});

export const getProjectWithCountsSchema = getProjectSchema.extend({
  teamSize: z
    .number()
    .describe(
      'Exists when "workers" search parameter is set to "none" or when skipped completely',
    ),
  reportCount: z
    .object({
      total: z.number(),
      submitted: z.number(),
      approved: z.number(),
      declined: z.number(),
      pending: z.number(),
    })
    .describe('Exists when "workers" search parameter is set to "none"'),
});

export const getProjectWithWorkerSchema = getProjectSchema.extend({
  workers: z.array(getWorkerWithUserSchema),
});

export const getProjectWithManagerSchema = getProjectWithCountsSchema.extend({
  manager: managerWithBasicUserSchema.nullable(),
});

export class ProjectDto extends createZodDto(getProjectWithCountsSchema) {}

export class ProjectWithTeamSizeDto extends createZodDto(
  getProjectSchema
    .omit({
      description: true,
      partnerId: true,
    })
    .extend({
      teamSize: z.number(),
    }),
) {}

export const setProjectManagerSchema = z.object({
  managerId: z.string(),
});

export class ProjectWithManagerDto extends createZodDto(
  getProjectWithManagerSchema,
) {}

export class SetProjectManagerDto extends createZodDto(
  setProjectManagerSchema,
) {}
