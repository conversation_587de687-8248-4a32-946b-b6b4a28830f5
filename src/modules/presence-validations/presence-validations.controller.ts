import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { IdDto } from '@/common/dto/id.dto';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { PresenceValidationDataDto } from './dto/presence-validation-data.dto';
import {
  PresenceValidationParamsDto,
  PresenceValidationParamsWithWorkerIdsDto,
} from './dto/presence-validation-params.dto';
import { PresenceValidationDto } from './dto/presence-validation.dto';
import { PresenceValidationsService } from './presence-validations.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';

@ApiTags('Presence Validations')
@ApiBearerAuth()
@Controller('presence-validations')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PresenceValidationsController {
  constructor(
    private readonly presenceValidationsService: PresenceValidationsService,
  ) {}
  // TODO protect this
  @Get('/worker/:workerId')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Get worker validations',
    description: 'Retrieve all presence validations for a specific worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Presence validations retrieved successfully',
    type: [PresenceValidationDto],
  })
  @ApiForbiddenResponse({
    description: 'Not authorized to access these validations',
  })
  @ApiNotFoundResponse({ description: 'Worker not found' })
  findAllForWorker(
    @Param('workerId') workerId: string,
  ): Promise<PresenceValidationDto[]> {
    return this.presenceValidationsService.findAllForWorker(workerId);
  }

  @Get('/worker/:workerId/today')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: "Get worker's today validations",
    description: "Retrieve today's presence validations for a specific worker",
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Today's presence validations retrieved successfully",
    type: [PresenceValidationDto],
  })
  @ApiNotFoundResponse({ description: 'Worker not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to access these validations',
  })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  findAllForWorkerToday(
    @Param('workerId') workerId: string,
  ): Promise<PresenceValidationDto[]> {
    return this.presenceValidationsService.findAllForWorker(workerId, true);
  }

  @Post('/worker/:workerId')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Create worker validation',
    description: 'Create a new presence validation for a specific worker',
  })
  @ApiParam({
    name: 'workerId',
    description: 'Worker unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Presence validation created successfully',
    type: IdDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid validation parameters' })
  @ApiNotFoundResponse({ description: 'Worker not found' })
  @ApiForbiddenResponse({ description: 'Not authorized to create validations' })
  createForUser(
    @Param('workerId') workerId: string,
    @User() user: RequestUserType,
    @Body() presenceValidationParams: PresenceValidationParamsDto,
  ): Promise<IdDto> {
    return this.presenceValidationsService.createForWorker(
      workerId,
      presenceValidationParams,
      user.id,
    );
  }

  @Post('/workers')
  @Roles(Role.Partner, Role.Manager)
  @ApiOperation({
    summary: 'Create bulk validations',
    description: 'Create presence validations for multiple workers',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Bulk presence validations created successfully',
    type: [String],
  })
  @ApiBadRequestResponse({ description: 'Invalid validation parameters' })
  @ApiForbiddenResponse({ description: 'Not authorized to create validations' })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  createForWorkers(
    @Body() presenceValidationParams: PresenceValidationParamsWithWorkerIdsDto,
    @User() user: RequestUserType,
  ): Promise<string[]> {
    return this.presenceValidationsService.createForWorkers(
      presenceValidationParams,
      user.id,
    );
  }

  @Get('/report/:id')
  @ApiOperation({
    summary: 'Get report validations',
    description: 'Retrieve all presence validations for a specific report',
  })
  @ApiParam({
    name: 'id',
    description: 'Report unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Report validations retrieved successfully',
    type: [PresenceValidationDto],
  })
  @ApiNotFoundResponse({ description: 'Report not found' })
  @ApiForbiddenResponse({ description: 'Not authorized to access this report' })
  findAllForReport(@Param('id') id: string): Promise<PresenceValidationDto[]> {
    return this.presenceValidationsService.findAllForReport(id);
  }

  @Get('/count/project/:projectId')
  @ApiOperation({
    summary: 'Get pending presence validations count',
    description:
      'Retrieve pending presence validations count for a specific project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Pending presence validations count retrieved successfully',
    type: Number,
  })
  @ApiNotFoundResponse({ description: 'Project not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this project',
  })
  findAllPendingCountForProject(
    @Param('projectId') projectId: string,
    @User() user: RequestUserType,
  ): Promise<number> {
    return this.presenceValidationsService.findAllPendingCountForProject(
      projectId,
      user.entityId,
    );
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get validation by ID',
    description: 'Retrieve a specific presence validation',
  })
  @ApiParam({
    name: 'id',
    description: 'Presence validation unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Presence validation retrieved successfully',
    type: PresenceValidationDto,
  })
  @ApiNotFoundResponse({ description: 'Validation not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to access this validation',
  })
  async findOne(@Param('id') id: string): Promise<PresenceValidationDto> {
    const validation = await this.presenceValidationsService.findOne(id);
    if (!validation) throw new NotFoundException('Validation not found');
    return validation;
  }

  @Post(':id')
  @HttpCode(HttpStatus.OK)
  @Roles(Role.Worker)
  @ApiOperation({
    summary: 'Submit validation data',
    description: 'Submit data for a specific presence validation',
  })
  @ApiParam({
    name: 'id',
    description: 'Presence validation unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Validation data submitted successfully',
  })
  @ApiBadRequestResponse({ description: 'Invalid validation data' })
  @ApiNotFoundResponse({ description: 'Validation not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to submit validation data',
  })
  sendValidationData(
    @Param('id') id: string,
    @Body() presenceValidationData: PresenceValidationDataDto,
  ) {
    return this.presenceValidationsService.setPresenceValidationData(
      id,
      presenceValidationData,
    );
  }
}
