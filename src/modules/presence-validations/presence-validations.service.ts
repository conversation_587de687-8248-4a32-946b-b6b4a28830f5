import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { addMinutes, isAfter, subMinutes } from 'date-fns';
import { and, eq, lt, sql } from 'drizzle-orm';

import { TIME_CONSTANTS } from '@/common/constants/validation.constants';
import { Forwarded } from '@/common/types';

import { DailyReportsService } from '../daily-reports/daily-reports.service';
import { Database } from '../db/db.module';
import { dailyReports } from '../db/entities/daily-report.entity';
import { presenceValidations } from '../db/entities/presence-validation.entity';
import { workers } from '../db/entities/worker.entity';
import { NotificationTopic } from '../notifications/dto/send-notification.dto';
import { NotificationsService } from '../notifications/notifications.service';
import { WorkersService } from '../workers/workers.service';
import { CreatePresenceValidationDto } from './dto/create-presence-validation.dto';
import { PresenceValidationDataDto } from './dto/presence-validation-data.dto';
import {
  PresenceValidationParamsDto,
  PresenceValidationParamsWithWorkerIdsDto,
} from './dto/presence-validation-params.dto';
import { UpdatePresenceValidationDto } from './dto/update-presence-validation.dto';

@Injectable()
export class PresenceValidationsService {
  private readonly logger = new Logger(PresenceValidationsService.name);
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => DailyReportsService))
    private readonly dailyReportsService: Forwarded<DailyReportsService>,
    private readonly notificationsService: NotificationsService,
    private readonly workersService: WorkersService,
  ) {}
  async create(createPresenceValidationDto: CreatePresenceValidationDto) {
    return (
      await this.db
        .insert(presenceValidations)
        .values(createPresenceValidationDto)
        .returning({
          id: presenceValidations.id,
        })
    )[0];
  }

  async createForWorker(
    workerId: string,
    presenceValidationParams: PresenceValidationParamsDto,
    supervisorId: string,
    reportId?: string,
    sendNotification = true,
    rateLimit = true,
  ) {
    if (!reportId) {
      const dailyReport =
        await this.dailyReportsService.findActiveByWorkerId(workerId);
      if (!dailyReport) throw new NotFoundException('Daily report not found');
      reportId = dailyReport.id;

      if (dailyReport.onPause) {
        throw new BadRequestException('Worker has paused report');
      }
    }

    if (rateLimit) {
      const presenceValidation = await this.findAllForReport(reportId);
      if (
        presenceValidation.some(
          (presenceValidation) =>
            isAfter(
              presenceValidation.createdAt,
              subMinutes(new Date(), TIME_CONSTANTS.RATE_LIMIT_MINUTES),
            ) && presenceValidation.reason === 'check',
        )
      )
        throw new BadRequestException(
          'You can only create one presence validation per 5 minutes',
        );
    }

    let topic: NotificationTopic | undefined;

    switch (true) {
      case presenceValidationParams.requiresPhoto:
        topic = 'validate-photo-presence';
        break;
      case presenceValidationParams.requiresGeo:
        topic = 'validate-geo-presence';
        break;
      case presenceValidationParams.requiresPhoto &&
        presenceValidationParams.requiresGeo:
        topic = 'validate-complete-presence';
        break;
      default:
        topic = undefined;
        break;
    }

    if (!topic)
      throw new BadRequestException(
        'At least one validation is required (photo or geo)',
      );

    const { id } = await this.create({
      ...presenceValidationParams,
      dailyReportId: reportId,
      requestedBy: supervisorId,
    });
    const worker = await this.workersService.findOne(workerId);
    if (!worker) throw new NotFoundException('Worker not found');

    if (sendNotification) {
      await this.notificationsService.send({
        title: 'Validate presence',
        body: 'Please validate your presence',
        receiverUserId: worker.userId,
        topic,
        data: {
          presenceValidationId: id,
        },
      });
    }
    await this.workersService.changePresenceValidationStatus(workerId, 'empty');
    await this.workersService.updateLastPresenceCheck(workerId);
    return { id };
  }

  async createForWorkers(
    presenceValidationParams: PresenceValidationParamsWithWorkerIdsDto,
    supervisorId: string,
    sendNotification = true,
    rateLimit = true,
  ) {
    const { workerIds } = presenceValidationParams;
    if (!workerIds || workerIds.length === 0) {
      throw new BadRequestException('Worker IDs cannot be empty.');
    }

    const dailyReports =
      await this.dailyReportsService.findActiveByWorkerIds(workerIds);
    const workersWithReports = workerIds.map((workerId) => {
      const dailyReport = dailyReports.find(
        (report) => report.authorId === workerId,
      );

      if (!dailyReport) {
        throw new NotFoundException(
          `No active report found for worker ${workerId}`,
        );
      }

      return {
        workerId,
        dailyReportId: dailyReport.id,
        reportOnPause: dailyReport.onPause,
      };
    });

    let topic: NotificationTopic | undefined;

    switch (true) {
      case presenceValidationParams.requiresPhoto:
        topic = 'validate-photo-presence';
        break;
      case presenceValidationParams.requiresGeo:
        topic = 'validate-geo-presence';
        break;
      case presenceValidationParams.requiresPhoto &&
        presenceValidationParams.requiresGeo:
        topic = 'validate-complete-presence';
        break;
      default:
        topic = undefined;
        break;
    }

    if (!topic)
      throw new BadRequestException(
        'At least one validation is required (photo or geo)',
      );

    const skippedWorkers: string[] = [];
    await Promise.all(
      workersWithReports.map(
        async ({ workerId, dailyReportId, reportOnPause }) => {
          if (reportOnPause) {
            skippedWorkers.push(workerId);
            return;
          }
          if (rateLimit) {
            const presenceValidation =
              await this.findAllForReport(dailyReportId);
            if (
              presenceValidation.some(
                (presenceValidation) =>
                  isAfter(
                    presenceValidation.createdAt,
                    subMinutes(new Date(), TIME_CONSTANTS.RATE_LIMIT_MINUTES),
                  ) && presenceValidation.reason === 'check',
              )
            ) {
              skippedWorkers.push(workerId);
              return;
            }
          }
          const { id } = await this.create({
            ...presenceValidationParams,
            dailyReportId,
            requestedBy: supervisorId,
          });

          const worker = await this.workersService.findOne(workerId);
          if (!worker) throw new NotFoundException('Worker not found');

          if (sendNotification) {
            await this.notificationsService.send({
              title: 'Validate presence',
              body: 'Please validate your presence',
              receiverUserId: worker.userId,
              topic,
              data: {
                presenceValidationId: id,
              },
            });
          }

          await this.workersService.changePresenceValidationStatus(
            workerId,
            'empty',
          );
          await this.workersService.updateLastPresenceCheck(workerId);
        },
      ),
    );
    return skippedWorkers;
  }

  findAllForReport(dailyReportId: string) {
    return this.db.query.presenceValidations.findMany({
      where: (presenceValidations, { eq }) =>
        eq(presenceValidations.dailyReportId, dailyReportId),
    });
  }

  async findAllForWorker(workerId: string, today: boolean = false) {
    let dailyReportId: string | undefined;
    if (today) {
      const activeDailyReport =
        await this.dailyReportsService.findActiveByWorkerId(workerId);
      if (!activeDailyReport)
        throw new NotFoundException('Daily report not found');
      dailyReportId = activeDailyReport.id;
    }

    return this.db
      .select({
        id: presenceValidations.id,
        dailyReportId: dailyReports.id,
        requestedBy: dailyReports.authorId,
        status: presenceValidations.status,
        requiresPhoto: presenceValidations.requiresPhoto,
        requiresGeo: presenceValidations.requiresGeo,
        photoId: presenceValidations.photoId,
        geoCoordinates: presenceValidations.geoCoordinates,
        geoApproved: presenceValidations.geoApproved,
        photoApproved: presenceValidations.photoApproved,
        reason: presenceValidations.reason,
        createdAt: presenceValidations.createdAt,
        sentAt: presenceValidations.sentAt,
        checkedAt: presenceValidations.checkedAt,
      })
      .from(presenceValidations)
      .innerJoin(
        dailyReports,
        eq(presenceValidations.dailyReportId, dailyReports.id),
      )
      .innerJoin(workers, eq(dailyReports.authorId, workers.id))
      .where(
        and(
          eq(workers.id, workerId),
          dailyReportId ? eq(dailyReports.id, dailyReportId) : undefined,
        ),
      );
  }

  async findAllPendingCountForProject(projectId: string, partnerId: string) {
    const currentTimePlus5Minutes = addMinutes(new Date(), 5);
    const [{ count }] = await this.db
      .select({
        count: sql<number>`count(presence_validation.id)`,
      })
      .from(presenceValidations)
      .innerJoin(
        dailyReports,
        eq(presenceValidations.dailyReportId, dailyReports.id),
      )
      .innerJoin(workers, eq(dailyReports.authorId, workers.id))
      .where(
        and(
          eq(workers.projectId, projectId),
          eq(workers.partnerId, partnerId),
          eq(presenceValidations.status, 'empty'),
          lt(presenceValidations.sentAt, currentTimePlus5Minutes),
          eq(workers.workingStatus, 'started'),
        ),
      );

    return count;
  }

  findOne(id: string) {
    return this.db.query.presenceValidations.findFirst({
      where: (presenceValidations, { eq }) => eq(presenceValidations.id, id),
    });
  }

  update(id: string, updatePresenceValidationDto: UpdatePresenceValidationDto) {
    return this.db.transaction(async (tx) => {
      const presenceValidation = await tx.query.presenceValidations.findFirst({
        where: (presenceValidations, { eq }) => eq(presenceValidations.id, id),
      });

      if (!presenceValidation) {
        throw new NotFoundException('Presence validation not found');
      }

      return tx
        .update(presenceValidations)
        .set(updatePresenceValidationDto)
        .where(eq(presenceValidations.id, id));
    });
  }

  changeStatus(
    id: string,
    status: 'sent' | 'confirmed' | 'declined' | 'sent_late' | 'empty',
    partnerUserId: string,
  ) {
    return this.db
      .update(presenceValidations)
      .set({
        status,
        checkedAt: ['confirmed', 'declined'].includes(status)
          ? new Date()
          : undefined,
        sentAt: ['sent', 'sent_late'].includes(status) ? new Date() : undefined,
      })
      .where(
        and(
          eq(presenceValidations.id, id),
          eq(presenceValidations.requestedBy, partnerUserId),
        ),
      );
  }

  // async checkPhoto(id: string, approved: boolean, partnerUserId: string) {
  //   const presenceValidation = await this.findOne(id);
  //   if (!presenceValidation?.requiresGeo && approved) {
  //     await this.changeStatus(id, 'confirmed', partnerUserId);
  //   }
  //   return this.db
  //     .update(presenceValidations)
  //     .set({
  //       photoApproved: approved,
  //       checkedAt: new Date(),
  //     })
  //     .where(
  //       and(
  //         eq(presenceValidations.id, id),
  //         eq(presenceValidations.requestedBy, partnerUserId),
  //       ),
  //     );
  // }

  // async checkGeo(id: string, approved: boolean, partnerUserId: string) {
  //   const presenceValidation = await this.findOne(id);
  //   if (!presenceValidation?.requiresPhoto && approved) {
  //     await this.changeStatus(id, 'confirmed', partnerUserId);
  //   }
  //   return this.db
  //     .update(presenceValidations)
  //     .set({
  //       geoApproved: approved,
  //       checkedAt: new Date(),
  //     })
  //     .where(
  //       and(
  //         eq(presenceValidations.id, id),
  //         eq(presenceValidations.requestedBy, partnerUserId),
  //       ),
  //     );
  // }

  async setPresenceValidationData(
    id: string,
    presenceValidationData: PresenceValidationDataDto,
  ) {
    const presenceValidation = await this.findOne(id);
    if (!presenceValidation)
      throw new NotFoundException('Presence validation not found');

    if (
      presenceValidation.requiresGeo &&
      !presenceValidationData.geoCoordinates
    )
      throw new BadRequestException('Geo coordinates are required');

    if (presenceValidation.requiresPhoto && !presenceValidationData.photoId)
      throw new BadRequestException('Photo is required');

    const now = new Date();
    const sentAt = presenceValidationData.sentAt;
    const timeDifference = Math.abs(now.getTime() - sentAt.getTime());

    if (timeDifference > TIME_CONSTANTS.PRESENCE_VALIDATION_WINDOW_MS) {
      throw new BadRequestException('Validation request has expired');
    }

    const isSentLate =
      presenceValidationData.sentAt.getTime() >
      new Date(
        presenceValidation.createdAt.getTime() +
          TIME_CONSTANTS.PRESENCE_VALIDATION_WINDOW_MS,
      ).getTime();

    if (isSentLate)
      throw new BadRequestException(
        'Too late, you cannot send a late presence validation',
      );

    await this.db
      .update(presenceValidations)
      .set({
        photoId: presenceValidationData.photoId,
        geoCoordinates: presenceValidationData.geoCoordinates,
        sentAt: presenceValidationData.sentAt,
        status: isSentLate ? 'sent_late' : 'sent',
      })
      .where(eq(presenceValidations.id, id));
    const dailyReport = await this.dailyReportsService.findOne(
      presenceValidation.dailyReportId,
    );
    if (!dailyReport)
      throw new NotFoundException(
        'Daily report for presence validation not found',
      );

    await this.workersService.changePresenceValidationStatus(
      dailyReport.authorId,
      isSentLate ? 'late' : 'validated',
    );
  }
}
