import {
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { asc, desc, eq } from 'drizzle-orm';

import { Database } from '../db/db.module';
import { CreateEmploymentHistoryDto } from './dto/create-employment-history.dto';
import { EmploymentHistoryFilterParamsDto } from './dto/employment-history-filter-params.dto';
import { employmentHistory } from '../db/entities/employment-history.entity';
import { ManagersService } from '../managers/managers.service';
import { Forwarded } from '@/common/types';

@Injectable()
export class EmploymentHistoryService {
  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => ManagersService))
    private readonly managersService: Forwarded<ManagersService>,
  ) {}

  async create(
    createEmploymentHistoryDto: CreateEmploymentHistoryDto,
    tx?: Database,
  ) {
    const dbOrTx = tx || this.db;
    return (
      await dbOrTx
        .insert(employmentHistory)
        .values(createEmploymentHistoryDto)
        .returning({ id: employmentHistory.id })
    )[0];
  }

  async findAll(
    user: {
      id: string;
      entityId: string;
      role: string;
      permissionType?: string;
      [key: string]: any;
    },
    filterParams?: EmploymentHistoryFilterParamsDto,
  ) {
    let partnerId: string | undefined;
    if (user.role === 'partner') {
      partnerId = user.entityId;
    } else if (user.role === 'manager') {
      const manager = await this.managersService.findOneByUserId(user.id);
      if (!manager) throw new NotFoundException('Manager not found');
      partnerId = manager.partnerId;
    } else {
      throw new ForbiddenException('Not authorized');
    }
    if (!partnerId) {
      throw new NotFoundException('Partner not found');
    }

    const { whereClauses, orderBy } = this.generateFilterClauses(filterParams);
    const employmentHistory = await this.db.query.employmentHistory.findMany({
      where: (employmentHistory, { eq, and }) =>
        and(eq(employmentHistory.partnerId, partnerId), ...whereClauses),
      orderBy,
      columns: {
        id: true,
        change: true,
        happenedAt: true,
      },
      with: {
        worker: {
          with: {
            user: {
              columns: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        manager: {
          with: {
            user: {
              columns: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });

    return employmentHistory.map(({ worker, manager, ...history }) => ({
      change: history.change,
      workerId: worker?.id,
      managerId: manager?.id,
      firstName: (worker?.user?.firstName ||
        manager?.user?.firstName) as string,
      lastName: (worker?.user?.lastName || manager?.user?.lastName) as string,
      role: worker?.id ? 'worker' : 'manager',
      happenedAt: history.happenedAt,
    }));
  }

  private generateFilterClauses(
    filterParams?: EmploymentHistoryFilterParamsDto,
  ) {
    const whereClauses = [
      filterParams?.change
        ? eq(employmentHistory.change, filterParams.change)
        : undefined,
    ];

    const sortBy = filterParams?.sortBy
      ? filterParams.sortBy === 'date'
        ? employmentHistory.happenedAt
        : filterParams.sortBy === 'change'
          ? employmentHistory.change
          : employmentHistory.happenedAt
      : employmentHistory.happenedAt;

    const orderBy =
      filterParams?.sortOrder && sortBy
        ? filterParams.sortOrder === 'asc'
          ? asc(sortBy)
          : desc(sortBy)
        : desc(employmentHistory.happenedAt);

    return { whereClauses, orderBy };
  }
}
