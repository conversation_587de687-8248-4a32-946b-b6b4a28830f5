import { Controller, Get, HttpStatus, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import { EmploymentHistoryDto } from './dto/employment-history.dto';
import { EmploymentHistoryService } from './employment-history.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
@ApiTags('Employment History')
@ApiBearerAuth()
@Controller('employment-history')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.Partner, {
  type: Role.Manager,
  settings: {
    permissionType: ManagerPermissionType.All,
  },
})
export class EmploymentHistoryController {
  constructor(
    private readonly employmentHistoryService: EmploymentHistoryService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all employment history',
    description:
      'Retrieve all employment history for the authenticated partner',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Employment history retrieved successfully',
    type: [EmploymentHistoryDto],
  })
  findAll(@User() user: RequestUserType): Promise<EmploymentHistoryDto[]> {
    return this.employmentHistoryService.findAll(user);
  }
}
