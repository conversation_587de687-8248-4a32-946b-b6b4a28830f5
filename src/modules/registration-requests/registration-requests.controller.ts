import {
  Controller,
  Get,
  HttpStatus,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';

import { Roles } from '@/common/decorators/roles.decorator';
import { User } from '@/common/decorators/user.decorator';
import { Role } from '@/common/enums';
import { RolesGuard } from '@/common/guards/roles.guard';

import {
  RegistrationRequestDto,
  WorkersRegistrationRequest,
} from './dto/registration-request.dto';
import { RegistrationRequestsService } from './registration-requests.service';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { JwtAuthGuard } from '../auth/guards/jwt.guard';
import { WorkerFilterParamsDto } from '../workers/dto/worker-filter-params.dto';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';

@ApiTags('Registration Requests')
@ApiBearerAuth()
@Controller('registration-requests')
@UseGuards(JwtAuthGuard, RolesGuard)
export class RegistrationRequestsController {
  constructor(
    private readonly registrationRequestsService: RegistrationRequestsService,
  ) {}

  @Get()
  @Roles(Role.Partner, {
    type: Role.Manager,
    settings: {
      permissionType: ManagerPermissionType.All,
    },
  })
  @ApiOperation({
    summary: 'List registration requests',
    description:
      'Retrieve all registration requests managed by the authenticated user',
  })
  @ApiQuery({ type: WorkerFilterParamsDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Registration requests retrieved successfully',
    type: [RegistrationRequestDto],
  })
  @ApiUnauthorizedResponse({ description: 'Not authenticated' })
  @ApiForbiddenResponse({ description: 'Not authorized to view requests' })
  getAll(
    @User() user: RequestUserType,
    @Query() filterParams?: WorkerFilterParamsDto,
  ): Promise<RegistrationRequestDto[]> {
    return this.registrationRequestsService.getAllForUser(user, filterParams);
  }

  @Get('me')
  @Roles(Role.Worker)
  @ApiOperation({
    summary: 'Get worker request',
    description:
      'Retrieve pending registration request for the authenticated worker',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Registration request retrieved successfully',
    type: WorkersRegistrationRequest,
  })
  @ApiNotFoundResponse({ description: 'No pending request found' })
  findWorkersRequest(
    @User() user: RequestUserType,
  ): Promise<WorkersRegistrationRequest> {
    return this.registrationRequestsService.findPendingForWorker(user.entityId);
  }

  @Patch(':id/approve')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Approve request',
    description: 'Approve a pending registration request',
  })
  @ApiParam({
    name: 'id',
    description: 'Registration request unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Request approved successfully',
  })
  @ApiNotFoundResponse({ description: 'Request not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to approve this request',
  })
  approve(@User() user: RequestUserType, @Param('id') id: string) {
    return this.registrationRequestsService.approve(id, user);
  }

  @Patch(':id/reject')
  @Roles(Role.Partner)
  @ApiOperation({
    summary: 'Reject request',
    description: 'Reject a pending registration request',
  })
  @ApiParam({
    name: 'id',
    description: 'Registration request unique identifier',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Request rejected successfully',
  })
  @ApiNotFoundResponse({ description: 'Request not found' })
  @ApiForbiddenResponse({
    description: 'Not authorized to reject this request',
  })
  reject(@User() user: RequestUserType, @Param('id') id: string) {
    return this.registrationRequestsService.reject(id, user);
  }
}
