import { Module, forwardRef } from '@nestjs/common';

import { RegistrationRequestsController } from './registration-requests.controller';
import { RegistrationRequestsService } from './registration-requests.service';
import { EmploymentHistoryModule } from '../employment-history/employment-history.module';
import { ManagersModule } from '../managers/managers.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { RegistrationCodesModule } from '../registration-codes/registration-codes.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => WorkersModule),
    NotificationsModule,
    EmploymentHistoryModule,
    forwardRef(() => RegistrationCodesModule),
  ],
  exports: [RegistrationRequestsService],
  providers: [RegistrationRequestsService],
  controllers: [RegistrationRequestsController],
})
export class RegistrationRequestsModule {}
