import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { eq } from 'drizzle-orm';

import { Database } from '../db/db.module';
import { CreatePartnerDto } from './dto/create-partner.dto';
import { UpdatePartnerDto } from './dto/update-partner.dto';
import { partners } from '../db/entities/partner.entity';

@Injectable()
export class PartnersService {
  constructor(@Inject('DB') private readonly db: Database) {}

  async create(createPartnerDto: CreatePartnerDto) {
    return (
      await this.db
        .insert(partners)
        .values(createPartnerDto)
        .returning({ id: partners.id })
    )[0];
  }

  findAll() {
    return this.db.query.partners.findMany();
  }

  findOne(id: string) {
    return this.db.query.partners.findFirst({
      where: (partners, { eq }) => eq(partners.id, id),
    });
  }

  findOneByUserId(userId: string) {
    return this.db.query.partners.findFirst({
      where: (partners, { eq }) => eq(partners.userId, userId),
    });
  }

  async findOneWithUser(id: string) {
    const partner = await this.db.query.partners.findFirst({
      where: eq(partners.id, id),
      columns: {
        taxNumber: true,
        registrationAddress: true,
        companyName: true,
      },
      with: {
        workers: {
          where: (workers, { eq, or }) =>
            or(
              eq(workers.employmentStatus, 'active'),
              eq(workers.employmentStatus, 'quit_notice'),
              eq(workers.employmentStatus, 'terminated_notice'),
            ),
          columns: {
            id: true,
          },
        },
        user: {
          columns: {
            id: false,
            hashedPassword: false,
            avatar: false,
            documentScan: false,
            isEmailVerified: false,
            isPhoneVerified: false,
            role: false,
          },
        },
      },
    });
    if (!partner) throw new NotFoundException('Partner not found');

    const { workers, ...rest } = partner;

    return {
      ...rest,
      workerCount: workers?.length,
    };
  }

  findByTaxNumber(taxNumber: string) {
    return this.db.query.partners.findFirst({
      where: (partners, { eq }) => eq(partners.taxNumber, taxNumber),
    });
  }

  update(id: string, updatePartnerDto: UpdatePartnerDto) {
    return this.db.transaction(async (tx) => {
      const partner = await tx.query.partners.findFirst({
        where: (partners, { eq }) => eq(partners.id, id),
      });

      if (!partner) {
        throw new NotFoundException('Partner not found');
      }

      return tx
        .update(partners)
        .set(updatePartnerDto)
        .where(eq(partners.id, id));
    });
  }

  updateByUserId(userId: string, updatePartnerDto: UpdatePartnerDto) {
    return this.db
      .update(partners)
      .set(updatePartnerDto)
      .where(eq(partners.userId, userId));
  }
}
